{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-receipt"></i> {{ title }}
                        <small class="text-muted float-end">
                            {{ sale.created_at.strftime('%d/%m/%Y %H:%M') }}
                        </small>
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Informations générales -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="mb-3">Informations générales</h6>
                            <table class="table table-sm">
                                <tr>
                                    <th style="width: 150px">Référence</th>
                                    <td>{{ sale.reference }}</td>
                                </tr>
                                <tr>
                                    <th>Statut</th>
                                    <td>
                                        {% if sale.status.value == 'pending' %}
                                        <span class="badge bg-warning">En attente</span>
                                        {% elif sale.status.value == 'paid' %}
                                        <span class="badge bg-success">Payée</span>
                                        {% elif sale.status.value == 'cancelled' %}
                                        <span class="badge bg-danger">Annulée</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Client</th>
                                    <td>
                                        {% if sale.customer %}
                                        {{ sale.customer.first_name }} {{ sale.customer.last_name }}
                                        {% else %}
                                        Client anonyme
                                        {% endif %}
                                    </td>
                                </tr>
                                {% if sale.table %}
                                <tr>
                                    <th>Table</th>
                                    <td>
                                        Table {{ sale.table.number }}
                                        {% if sale.table.room %}({{ sale.table.room.name }}){% endif %}
                                        {% if sale.table.location %} - {{ sale.table.location }}{% endif %}
                                        - {{ sale.table.capacity }} personnes
                                    </td>
                                </tr>
                                {% endif %}
                                {% if sale.service_type %}
                                <tr>
                                    <th>Type de service</th>
                                    <td>{{ sale.service_type_display }}</td>
                                </tr>
                                {% endif %}
                                {% if sale.covers_count %}
                                <tr>
                                    <th>Couverts</th>
                                    <td>{{ sale.covers_count }} personne(s)</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="mb-3">Paiement</h6>
                            <table class="table table-sm">
                                <tr>
                                    <th style="width: 150px">Sous-total</th>
                                    <td>{{ "%.2f"|format(sale.subtotal) }} €</td>
                                </tr>
                                {% if sale.tax_amount > 0 %}
                                <tr>
                                    <th>TVA ({{ "%.1f"|format(sale.tax_rate) }}%)</th>
                                    <td>{{ "%.2f"|format(sale.tax_amount) }} €</td>
                                </tr>
                                {% endif %}
                                {% if sale.discount_amount > 0 %}
                                <tr>
                                    <th>Remise</th>
                                    <td>-{{ "%.2f"|format(sale.discount_amount) }} €</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <th>Total</th>
                                    <td class="fw-bold">{{ "%.2f"|format(sale.total) }} €</td>
                                </tr>
                                {% if sale.payments %}
                                <tr>
                                    <th>Payé</th>
                                    <td class="text-success">{{ "%.2f"|format(sale.total_paid) }} €</td>
                                </tr>
                                {% if sale.remaining_amount > 0 %}
                                <tr>
                                    <th>Restant</th>
                                    <td class="text-warning">{{ "%.2f"|format(sale.remaining_amount) }} €</td>
                                </tr>
                                {% endif %}
                                {% endif %}
                                {% if sale.payment_method %}
                                <tr>
                                    <th>Mode de paiement</th>
                                    <td>{{ sale.payment_method.value|upper }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>

                    <!-- Articles -->
                    <h6 class="mb-3">Articles</h6>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th class="text-end">Prix unitaire</th>
                                    <th class="text-end">Quantité</th>
                                    <th class="text-end">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in sale.items %}
                                <tr>
                                    <td>{{ item.product.name }}</td>
                                    <td class="text-end">{{ "%.2f"|format(item.price) }} €</td>
                                    <td class="text-end">{{ item.quantity }}</td>
                                    <td class="text-end">{{ "%.2f"|format(item.total) }} €</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3" class="text-end">Sous-total</th>
                                    <th class="text-end">{{ "%.2f"|format(sale.subtotal) }} €</th>
                                </tr>
                                {% if sale.tax_amount > 0 %}
                                <tr>
                                    <th colspan="3" class="text-end">TVA ({{ "%.1f"|format(sale.tax_rate) }}%)</th>
                                    <th class="text-end">{{ "%.2f"|format(sale.tax_amount) }} €</th>
                                </tr>
                                {% endif %}
                                {% if sale.discount_amount > 0 %}
                                <tr>
                                    <th colspan="3" class="text-end">Remise</th>
                                    <th class="text-end">-{{ "%.2f"|format(sale.discount_amount) }} €</th>
                                </tr>
                                {% endif %}
                                <tr>
                                    <th colspan="3" class="text-end">Total</th>
                                    <th class="text-end">{{ "%.2f"|format(sale.total) }} €</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    {% if sale.payments and sale.payments|length > 0 %}
                    <!-- Historique des paiements -->
                    <div class="mt-4">
                        <h6 class="mb-3">Historique des paiements</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Méthode</th>
                                        <th class="text-end">Montant</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for payment in sale.payments %}
                                    <tr>
                                        <td>{{ payment.created_at.strftime('%d/%m/%Y %H:%M') if payment.created_at else '-' }}</td>
                                        <td>
                                            <span class="badge bg-primary">{{ payment.method.value|upper }}</span>
                                        </td>
                                        <td class="text-end">{{ "%.2f"|format(payment.amount) }} €</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot>
                                    <tr class="table-success">
                                        <th colspan="2">Total payé</th>
                                        <th class="text-end">{{ "%.2f"|format(sale.total_paid) }} €</th>
                                    </tr>
                                    {% if sale.remaining_amount > 0 %}
                                    <tr class="table-warning">
                                        <th colspan="2">Restant à payer</th>
                                        <th class="text-end">{{ "%.2f"|format(sale.remaining_amount) }} €</th>
                                    </tr>
                                    {% endif %}
                                </tfoot>
                            </table>
                        </div>
                    </div>
                    {% endif %}

                    {% if sale.kitchen_note %}
                    <!-- Note cuisine -->
                    <div class="mt-4">
                        <h6 class="mb-2">Note cuisine</h6>
                        <div class="alert alert-info">
                            {{ sale.kitchen_note }}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('pos.sales') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Retour à la liste
                        </a>
                        {% if sale.status.value == 'pending' %}
                        <button class="btn btn-danger" onclick="cancelSale()">
                            <i class="fas fa-times"></i> Annuler la vente
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
function cancelSale() {
    if (confirm('Êtes-vous sûr de vouloir annuler cette vente ?')) {
        fetch(`/pos/sales/{{ sale.id }}/cancel`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Une erreur est survenue');
        });
    }
}
</script>
{% endblock %}
{% endblock %}