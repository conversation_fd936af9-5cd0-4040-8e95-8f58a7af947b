#!/usr/bin/env python3
"""
Script d'initialisation pour créer les tables et colonnes nécessaires
au système de gestion des salles et tables.
"""

import os
import sys
from datetime import datetime

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.modules.auth.models import User
from app.modules.tables.models_table import Room, Table
from app.modules.pos.models_sale import Sale

def init_database():
    """Initialise la base de données avec les nouvelles tables et colonnes"""
    app = create_app()
    
    with app.app_context():
        print("🔧 Initialisation de la base de données...")
        
        # Créer toutes les tables
        db.create_all()
        print("✅ Tables créées")
        
        # Vérifier s'il y a des utilisateurs
        users = User.query.all()
        if not users:
            print("❌ Aucun utilisateur trouvé. Veuillez d'abord créer un utilisateur.")
            return
        
        print(f"👥 {len(users)} utilisateur(s) trouvé(s)")
        
        # Pour chaque utilisateur, créer une salle par défaut si elle n'existe pas
        for user in users:
            print(f"\n🔄 Traitement de l'utilisateur: {user.username}")
            
            # Vérifier si l'utilisateur a déjà des salles
            existing_rooms = Room.query.filter_by(owner_id=user.id).count()
            
            if existing_rooms == 0:
                # Créer une salle par défaut
                default_room = Room(
                    owner_id=user.id,
                    name="Salle principale",
                    description="Salle par défaut créée automatiquement",
                    width=800,
                    height=600,
                    background_color="#f8f9fa",
                    is_default=True,
                    is_active=True
                )
                db.session.add(default_room)
                db.session.flush()  # Pour obtenir l'ID
                
                print(f"  ✅ Salle par défaut créée: {default_room.name}")
                
                # Migrer les tables existantes vers cette salle
                existing_tables = Table.query.filter_by(owner_id=user.id).all()
                
                for i, table in enumerate(existing_tables):
                    # Assigner la salle
                    table.room_id = default_room.id
                    
                    # Définir des valeurs par défaut pour les nouvelles colonnes
                    if not hasattr(table, 'current_covers') or table.current_covers is None:
                        table.current_covers = 0
                    if not hasattr(table, 'position_x') or table.position_x is None:
                        table.position_x = 100 + (i % 5) * 120  # Disposition en grille
                    if not hasattr(table, 'position_y') or table.position_y is None:
                        table.position_y = 100 + (i // 5) * 120
                    if not hasattr(table, 'table_shape') or table.table_shape is None:
                        table.table_shape = 'round'
                    if not hasattr(table, 'table_size') or table.table_size is None:
                        table.table_size = 'medium'
                    if not hasattr(table, 'table_color') or table.table_color is None:
                        table.table_color = '#8B4513'
                    if not hasattr(table, 'created_at') or table.created_at is None:
                        table.created_at = datetime.utcnow()
                    if not hasattr(table, 'updated_at') or table.updated_at is None:
                        table.updated_at = datetime.utcnow()
                
                print(f"  ✅ {len(existing_tables)} table(s) migrée(s) vers la salle par défaut")
                
                # Créer quelques tables d'exemple si aucune table n'existe
                if len(existing_tables) == 0:
                    sample_tables = [
                        {"number": "1", "capacity": 2, "x": 100, "y": 100},
                        {"number": "2", "capacity": 4, "x": 250, "y": 100},
                        {"number": "3", "capacity": 4, "x": 400, "y": 100},
                        {"number": "4", "capacity": 6, "x": 100, "y": 250},
                        {"number": "5", "capacity": 8, "x": 250, "y": 250},
                    ]
                    
                    for table_data in sample_tables:
                        table = Table(
                            owner_id=user.id,
                            room_id=default_room.id,
                            number=table_data["number"],
                            capacity=table_data["capacity"],
                            position_x=table_data["x"],
                            position_y=table_data["y"],
                            table_shape='round',
                            table_size='medium',
                            table_color='#8B4513',
                            current_covers=0
                        )
                        db.session.add(table)
                    
                    print(f"  ✅ {len(sample_tables)} table(s) d'exemple créée(s)")
            
            else:
                print(f"  ℹ️  L'utilisateur a déjà {existing_rooms} salle(s)")
                
                # Vérifier et mettre à jour les tables existantes
                tables_to_update = Table.query.filter_by(owner_id=user.id).all()
                updated_count = 0
                
                for table in tables_to_update:
                    updated = False
                    
                    if not hasattr(table, 'current_covers') or table.current_covers is None:
                        table.current_covers = 0
                        updated = True
                    if not hasattr(table, 'position_x') or table.position_x is None:
                        table.position_x = 100
                        updated = True
                    if not hasattr(table, 'position_y') or table.position_y is None:
                        table.position_y = 100
                        updated = True
                    if not hasattr(table, 'table_shape') or table.table_shape is None:
                        table.table_shape = 'round'
                        updated = True
                    if not hasattr(table, 'table_size') or table.table_size is None:
                        table.table_size = 'medium'
                        updated = True
                    if not hasattr(table, 'table_color') or table.table_color is None:
                        table.table_color = '#8B4513'
                        updated = True
                    if not hasattr(table, 'created_at') or table.created_at is None:
                        table.created_at = datetime.utcnow()
                        updated = True
                    if not hasattr(table, 'updated_at') or table.updated_at is None:
                        table.updated_at = datetime.utcnow()
                        updated = True
                    
                    if updated:
                        updated_count += 1
                
                if updated_count > 0:
                    print(f"  ✅ {updated_count} table(s) mise(s) à jour avec les nouvelles colonnes")
            
            # Mettre à jour les ventes existantes
            sales_to_update = Sale.query.filter_by(owner_id=user.id).all()
            sales_updated = 0
            
            for sale in sales_to_update:
                updated = False
                
                if not hasattr(sale, 'covers_count') or sale.covers_count is None:
                    sale.covers_count = 1
                    updated = True
                if not hasattr(sale, 'service_type') or sale.service_type is None:
                    sale.service_type = 'dine_in'
                    updated = True
                
                if updated:
                    sales_updated += 1
            
            if sales_updated > 0:
                print(f"  ✅ {sales_updated} vente(s) mise(s) à jour avec les nouvelles colonnes")
        
        # Sauvegarder toutes les modifications
        try:
            db.session.commit()
            print("\n🎉 Initialisation terminée avec succès!")
            print("\nVous pouvez maintenant:")
            print("1. Accéder à la gestion des salles: /rooms/")
            print("2. Utiliser le nouveau sélecteur de service dans le POS")
            print("3. Gérer les plans de salle avec glisser-déposer")
            
        except Exception as e:
            db.session.rollback()
            print(f"\n❌ Erreur lors de la sauvegarde: {str(e)}")
            raise

if __name__ == "__main__":
    print("🚀 Initialisation du système de gestion des salles et tables")
    print("=" * 60)
    
    try:
        init_database()
    except Exception as e:
        print(f"\n💥 Erreur fatale: {str(e)}")
        sys.exit(1)
