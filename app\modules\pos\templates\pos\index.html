{% extends "base.html" %}

{% block title %}Point de Vente{% endblock %}

{% block head_scripts %}
<script src="{{ url_for('pos.static', filename='js/pos.js') }}"></script>
<script src="{{ url_for('pos.static', filename='js/main.js') }}"></script>
<script src="{{ url_for('pos.static', filename='js/cart-ui.js') }}"></script>
{% endblock %}

{% block extra_css %}
<style>
/* Styles de base et variables */
:root {
    --header-height: 60px;
    --sidebar-width: 280px;
    --cart-width: 320px;
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --light-color: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
    --radius: 8px;
}

/* Barre de navigation POS */
.pos-navbar {
    background-color: white;
    border-bottom: 2px solid var(--border-color);
    box-shadow: var(--shadow);
    margin-bottom: 10px;
}

.pos-nav-buttons .btn {
    font-size: 13px;
    padding: 6px 12px;
}

/* Layout responsive */
.main-content {
    display: flex;
    height: calc(100vh - var(--header-height));
    gap: 10px;
    padding: 10px;
    position: relative;
    max-width: 100%;
    margin: 0;
}

@media (max-width: 1200px) {
    .main-content {
        flex-direction: column;
        height: auto;
    }

    .left-panel, .right-panel {
        width: 100%;
    }

    .cart-container {
        height: 400px;
    }
}

/* Panneau gauche */
.left-panel {
    width: var(--sidebar-width);
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* Panneau central */
.center-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    background-color: var(--light-color);
    border-radius: var(--radius);
    padding: 10px;
}

/* Panneau droit */
.right-panel {
    width: var(--cart-width);
}

/* Barre de recherche */
.search-bar {
    margin-bottom: 10px;
}

.search-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    font-size: 14px;
}

/* Catégories */
.category-tabs {
    display: flex;
    overflow-x: auto;
    gap: 5px;
    padding: 5px 0;
    margin-bottom: 10px;
    scrollbar-width: thin;
    -webkit-overflow-scrolling: touch;
}

.category-tab {
    padding: 6px 12px;
    border-radius: 15px;
    background-color: var(--light-color);
    white-space: nowrap;
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid var(--border-color);
    font-size: 14px;
}

.category-tab.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Grille de produits */
.products-container {
    flex: 1;
    overflow-y: auto;
    background-color: white;
    border-radius: var(--radius);
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 8px;
    padding: 8px;
}

.product-btn {
    height: 100px;
    border-radius: var(--radius);
    border: 1px solid var(--border-color);
    background-color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 5px;
    transition: all 0.2s;
    text-align: center;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.product-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.product-btn .image-container {
    width: 50px;
    height: 50px;
    margin-bottom: 5px;
    border-radius: var(--radius);
    overflow: hidden;
}

.product-btn img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-btn .product-name {
    font-weight: bold;
    font-size: 12px;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 2px;
}

.product-btn .price {
    font-size: 14px;
    color: var(--success-color);
    font-weight: bold;
}

.product-btn .stock {
    font-size: 10px;
    color: var(--secondary-color);
    position: absolute;
    bottom: 2px;
    right: 2px;
    background-color: var(--light-color);
    padding: 1px 4px;
    border-radius: 8px;
}

.stock-warning {
    color: var(--danger-color);
}

.stock-updated {
    animation: highlight 2s ease-in-out;
}

@keyframes highlight {
    0% {
        background-color: transparent;
    }
    50% {
        background-color: rgba(255, 193, 7, 0.3);
    }
    100% {
        background-color: transparent;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Barre de navigation POS -->
<div class="pos-navbar">
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center py-2">
            <h4 class="mb-0">
                <i class="fas fa-cash-register"></i> Point de Vente
            </h4>
            <div class="pos-nav-buttons">
                <a href="{{ url_for('pos.sales') }}" class="btn btn-outline-primary btn-sm me-2">
                    <i class="fas fa-list"></i> Ventes
                </a>
                <a href="{{ url_for('pos.kitchen_orders') }}" class="btn btn-outline-warning btn-sm me-2">
                    <i class="fas fa-utensils"></i> Cuisine
                </a>
                <a href="{{ url_for('pos.ready_orders') }}" class="btn btn-outline-success btn-sm">
                    <i class="fas fa-bell"></i> Commandes prêtes
                </a>
            </div>
        </div>
    </div>
</div>

<div class="main-content">
    <!-- Panneau gauche -->
    <div class="left-panel">
        <!-- Pavé numérique et autres contrôles -->
        {% include 'pos/_numpad.html' %}
    </div>

    <!-- Panneau central -->
    <div class="center-panel">
        <!-- Barre de recherche -->
        <div class="search-bar">
            <input type="text" class="search-input" placeholder="Rechercher un produit...">
        </div>

        <!-- Catégories -->
        <div class="category-tabs">
            <div class="category-tab active" data-category="all">Tous</div>
            {% for category in categories %}
            <div class="category-tab" data-category="{{ category.id }}">
                {{ category.name }}
            </div>
            {% endfor %}
        </div>

        <!-- Grille de produits -->
        <div class="products-container">
            <div class="product-grid">
                {% for product in products %}
                <div class="product-btn" data-product-id="{{ product.id }}"
                     data-category="{{ product.category_id }}">
                    <div class="image-container">
                        {% if product.image_path %}
                        <img src="{{ url_for('static', filename=product.image_path) }}"
                             alt="{{ product.name }}">
                        {% else %}
                        <img src="{{ url_for('static', filename='img/default-product.png') }}"
                             alt="Image par défaut">
                        {% endif %}
                    </div>
                    <div class="product-name">{{ product.name }}</div>
                    <div class="price">{{ "%.2f"|format(product.price) }} €</div>
                    <div class="stock {% if product.get_stock_status() != 'in_stock' %}stock-warning{% endif %}">
                        {{ product.get_available_quantity()|round(1) }}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Panneau droit (panier) -->
    <div class="right-panel">
        {% include 'pos/_cart.html' %}
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Fonction pour mettre à jour la grille de produits
    function updateProductGrid(products) {
        const grid = document.querySelector('.product-grid');
        grid.innerHTML = '';

        products.forEach(product => {
            const productElement = document.createElement('div');
            productElement.className = 'product-btn';
            productElement.dataset.productId = product.id;

            const stockStatus = product.stock_quantity <= product.minimum_stock ? 'stock-warning' : '';

            productElement.innerHTML = `
                <div class="image-container">
                    <img src="${product.image_path}" alt="${product.name}">
                </div>
                <div class="product-name">${product.name}</div>
                <div class="price">${product.price.toFixed(2)} €</div>
                <div class="stock ${stockStatus}">
                    ${product.stock_quantity.toFixed(1)}
                </div>
            `;

            grid.appendChild(productElement);
        });
    }

    // Gestionnaire de clic pour les onglets de catégorie
    document.querySelectorAll('.category-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            // Retirer la classe active de tous les onglets
            document.querySelectorAll('.category-tab').forEach(t => t.classList.remove('active'));
            // Ajouter la classe active à l'onglet cliqué
            this.classList.add('active');

            // Récupérer l'ID de la catégorie
            const categoryId = this.dataset.category === 'all' ? 0 : parseInt(this.dataset.category);

            // Appeler l'API pour obtenir les produits de la catégorie
            fetch(`/pos/get_products/${categoryId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateProductGrid(data.data);
                    }
                })
                .catch(error => console.error('Erreur:', error));
        });
    });
});
</script>
{% endblock %}