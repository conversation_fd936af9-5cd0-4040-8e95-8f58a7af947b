/**
 * Gestionnaire de sélection de service et de tables
 */
class ServiceSelector {
    constructor() {
        this.selectedService = null;
        this.selectedRoom = null;
        this.selectedTable = null;
        this.selectedCovers = 1;
        this.rooms = [];
        this.currentRoomTables = [];
        
        this.initializeEventListeners();
        this.loadRooms();
    }

    initializeEventListeners() {
        // Sélection du type de service
        document.addEventListener('click', (e) => {
            if (e.target.closest('.service-option')) {
                this.selectService(e.target.closest('.service-option'));
            }
        });

        // Sélection de salle
        document.addEventListener('click', (e) => {
            if (e.target.closest('[data-room-id]')) {
                this.selectRoom(e.target.closest('[data-room-id]').dataset.roomId);
            }
        });

        // Sélection de table
        document.addEventListener('click', (e) => {
            if (e.target.closest('.table-mini')) {
                this.selectTable(e.target.closest('.table-mini'));
            }
        });

        // Sélection du nombre de couverts
        document.addEventListener('click', (e) => {
            if (e.target.closest('[data-covers]')) {
                this.selectCovers(e.target.closest('[data-covers]').dataset.covers);
            }
        });

        // Confirmation des couverts
        document.getElementById('confirmCovers')?.addEventListener('click', () => {
            this.confirmCovers();
        });

        // Input personnalisé pour les couverts
        document.getElementById('customCovers')?.addEventListener('input', (e) => {
            this.selectCovers(e.target.value);
        });
    }

    async loadRooms() {
        try {
            const response = await fetch('/pos/api/get_rooms');
            if (response.ok) {
                this.rooms = await response.json();
            }
        } catch (error) {
            console.error('Erreur lors du chargement des salles:', error);
        }
    }

    selectService(serviceElement) {
        // Désélectionner les autres options
        document.querySelectorAll('.service-card').forEach(card => {
            card.classList.remove('selected');
        });

        // Sélectionner la nouvelle option
        serviceElement.querySelector('.service-card').classList.add('selected');
        this.selectedService = serviceElement.dataset.service;

        // Si c'est "sur place", ouvrir la sélection de table
        if (this.selectedService === 'dine_in') {
            setTimeout(() => {
                this.openRoomTableModal();
            }, 500);
        } else {
            // Pour les autres services, aller directement aux couverts
            setTimeout(() => {
                this.openCoversModal();
            }, 500);
        }
    }

    async openRoomTableModal() {
        const modal = new bootstrap.Modal(document.getElementById('roomTableModal'));
        
        // Charger les salles
        await this.loadRoomSelector();
        
        // Sélectionner la salle par défaut
        const defaultRoom = this.rooms.find(room => room.is_default) || this.rooms[0];
        if (defaultRoom) {
            await this.selectRoom(defaultRoom.id);
        }
        
        modal.show();
    }

    async loadRoomSelector() {
        const roomSelector = document.getElementById('roomSelector');
        roomSelector.innerHTML = '';

        this.rooms.forEach(room => {
            const button = document.createElement('button');
            button.type = 'button';
            button.className = 'btn btn-outline-primary';
            button.dataset.roomId = room.id;
            button.innerHTML = `${room.name} <span class="badge bg-secondary">${room.table_count}</span>`;
            roomSelector.appendChild(button);
        });
    }

    async selectRoom(roomId) {
        // Mettre à jour l'interface
        document.querySelectorAll('[data-room-id]').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-room-id="${roomId}"]`)?.classList.add('active');

        this.selectedRoom = roomId;

        // Charger les tables de la salle
        await this.loadRoomTables(roomId);
    }

    async loadRoomTables(roomId) {
        try {
            const response = await fetch(`/rooms/api/get_room_data/${roomId}`);
            if (response.ok) {
                const data = await response.json();
                this.currentRoomTables = data.tables;
                this.renderRoomPlan(data.room, data.tables);
            }
        } catch (error) {
            console.error('Erreur lors du chargement des tables:', error);
        }
    }

    renderRoomPlan(room, tables) {
        const roomPlan = document.getElementById('roomPlan');
        roomPlan.innerHTML = '';

        // Créer le conteneur du plan
        const planContainer = document.createElement('div');
        planContainer.style.position = 'relative';
        planContainer.style.width = Math.min(room.width, 800) + 'px';
        planContainer.style.height = Math.min(room.height, 400) + 'px';
        planContainer.style.backgroundColor = room.background_color;
        planContainer.style.border = '2px solid #dee2e6';
        planContainer.style.margin = '0 auto';
        planContainer.style.overflow = 'hidden';

        // Ajouter les tables
        tables.forEach(table => {
            const tableElement = document.createElement('div');
            tableElement.className = `table-mini ${table.status}`;
            tableElement.dataset.tableId = table.id;
            tableElement.style.position = 'absolute';
            tableElement.style.left = (table.position_x * Math.min(room.width, 800) / room.width) + 'px';
            tableElement.style.top = (table.position_y * Math.min(room.height, 400) / room.height) + 'px';
            tableElement.textContent = table.number;
            
            // Ajouter les informations de la table
            if (table.current_covers > 0) {
                tableElement.title = `Table ${table.number} - ${table.current_covers} couverts - ${table.current_amount}€`;
            } else {
                tableElement.title = `Table ${table.number} - Libre`;
            }

            // Désactiver les tables occupées
            if (table.status !== 'available') {
                tableElement.style.opacity = '0.5';
                tableElement.style.cursor = 'not-allowed';
            }

            planContainer.appendChild(tableElement);
        });

        roomPlan.appendChild(planContainer);
    }

    selectTable(tableElement) {
        const tableId = tableElement.dataset.tableId;
        const table = this.currentRoomTables.find(t => t.id == tableId);

        // Vérifier si la table est disponible
        if (table.status !== 'available') {
            alert('Cette table n\'est pas disponible');
            return;
        }

        // Désélectionner les autres tables
        document.querySelectorAll('.table-mini').forEach(t => {
            t.classList.remove('selected');
        });

        // Sélectionner la nouvelle table
        tableElement.classList.add('selected');
        this.selectedTable = table;

        // Fermer le modal de sélection de table et ouvrir celui des couverts
        bootstrap.Modal.getInstance(document.getElementById('roomTableModal')).hide();
        setTimeout(() => {
            this.openCoversModal();
        }, 300);
    }

    openCoversModal() {
        const modal = new bootstrap.Modal(document.getElementById('coversModal'));
        
        // Réinitialiser la sélection
        document.querySelectorAll('.cover-option').forEach(option => {
            option.classList.remove('selected');
        });
        document.getElementById('customCovers').value = '';
        
        modal.show();
    }

    selectCovers(covers) {
        this.selectedCovers = parseInt(covers);

        // Mettre à jour l'interface
        document.querySelectorAll('.cover-option').forEach(option => {
            option.classList.remove('selected');
        });

        const selectedOption = document.querySelector(`[data-covers="${covers}"]`);
        if (selectedOption) {
            selectedOption.classList.add('selected');
        }

        // Vider l'input personnalisé si on sélectionne un bouton
        if (selectedOption) {
            document.getElementById('customCovers').value = '';
        }
    }

    confirmCovers() {
        // Vérifier l'input personnalisé
        const customCovers = document.getElementById('customCovers').value;
        if (customCovers) {
            this.selectedCovers = parseInt(customCovers);
        }

        if (this.selectedCovers < 1) {
            alert('Veuillez sélectionner un nombre de couverts valide');
            return;
        }

        // Fermer le modal et procéder à la commande
        bootstrap.Modal.getInstance(document.getElementById('coversModal')).hide();
        
        // Fermer aussi le modal de service
        const serviceModal = bootstrap.Modal.getInstance(document.getElementById('serviceModal'));
        if (serviceModal) {
            serviceModal.hide();
        }

        // Procéder à la création de la commande
        this.proceedToOrder();
    }

    proceedToOrder() {
        // Préparer les données de la commande
        const orderData = {
            service_type: this.selectedService,
            covers_count: this.selectedCovers,
            table_id: this.selectedTable?.id || null,
            room_id: this.selectedRoom || null
        };

        // Stocker les données dans le sessionStorage pour la page de commande
        sessionStorage.setItem('newOrderData', JSON.stringify(orderData));

        // Afficher un message de confirmation
        const serviceName = this.getServiceName(this.selectedService);
        let message = `Nouvelle commande: ${serviceName}`;
        
        if (this.selectedTable) {
            message += ` - Table ${this.selectedTable.number}`;
        }
        message += ` - ${this.selectedCovers} couvert(s)`;

        // Afficher dans l'interface POS
        this.displayOrderInfo(orderData);

        // Réinitialiser la sélection
        this.resetSelection();
    }

    getServiceName(serviceType) {
        const serviceNames = {
            'dine_in': 'Sur place',
            'takeaway': 'À emporter',
            'delivery': 'Livraison',
            'drive_thru': 'Service au volant'
        };
        return serviceNames[serviceType] || serviceType;
    }

    displayOrderInfo(orderData) {
        // Mettre à jour l'interface POS avec les informations de commande
        const orderInfoElement = document.getElementById('current-order-info');
        if (orderInfoElement) {
            let info = `<strong>${this.getServiceName(orderData.service_type)}</strong>`;
            
            if (orderData.table_id && this.selectedTable) {
                info += ` - Table ${this.selectedTable.number}`;
            }
            info += ` - ${orderData.covers_count} couvert(s)`;
            
            orderInfoElement.innerHTML = info;
        }

        // Activer le panier si ce n'est pas déjà fait
        if (typeof POS !== 'undefined') {
            POS.setOrderData(orderData);
        }
    }

    resetSelection() {
        this.selectedService = null;
        this.selectedRoom = null;
        this.selectedTable = null;
        this.selectedCovers = 1;
    }

    // Méthode publique pour ouvrir le sélecteur de service
    static openServiceSelector() {
        const modal = new bootstrap.Modal(document.getElementById('serviceModal'));
        modal.show();
    }
}

// Initialiser le sélecteur de service
document.addEventListener('DOMContentLoaded', function() {
    window.serviceSelector = new ServiceSelector();
});

// Fonction globale pour ouvrir le sélecteur
window.openServiceSelector = function() {
    ServiceSelector.openServiceSelector();
};
